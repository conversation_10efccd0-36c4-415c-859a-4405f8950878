<?php
/**
 * Custom WooCommerce Shop Page Template
 * Enhanced version with advanced filtering and responsive grid
 *
 * @package BreakdanceZeroTheme
 */

// Security check
if (!defined('ABSPATH')) {
    exit;
}

// Ensure WooCommerce is loaded
if (!function_exists('woocommerce_breadcrumb')) {
    return;
}

// Get current query object for context
$current_cat = isset($current_cat) ? $current_cat : get_queried_object();
$is_category = isset($is_category) ? $is_category : ($current_cat instanceof WP_Term && $current_cat->taxonomy === 'product_cat');
$shop_page_id = wc_get_page_id('shop');
$products_per_page = wc_get_default_products_per_row() * wc_get_default_product_rows_per_page();

echo '<div class="custom-shop-container">';

// Mobile filter toggle
echo '<button class="mobile-filter-toggle" aria-label="' . esc_attr__('Filters tonen/verbergen', 'textdomain') . '">';
echo '<span class="filter-icon">⚙️</span>';
echo '<span class="filter-text">' . esc_html__('Filters', 'textdomain') . '</span>';
echo '</button>';

// Sidebar with advanced filters
echo '<aside class="custom-shop-sidebar" id="shop-sidebar">';
echo '<div class="sidebar-content">';

// Active filters
echo '<div class="active-filters-section">';
echo '<div class="active-filters-list">';

$active_filters = array();

// Price filter
if (isset($_GET['min_price']) || isset($_GET['max_price'])) {
    $min = isset($_GET['min_price']) ? wc_clean(wp_unslash($_GET['min_price'])) : '';
    $max = isset($_GET['max_price']) ? wc_clean(wp_unslash($_GET['max_price'])) : '';
    if ($min || $max) {
        $active_filters[] = sprintf(
            __('Prijs: €%s - €%s', 'textdomain'),
            $min ?: '0',
            $max ?: '∞'
        );
    }
}

// Category filter
if ($is_category) {
    $active_filters[] = sprintf(__('Categorie: %s', 'textdomain'), esc_html($current_cat->name));
}

// Attribute filters
foreach ($_GET as $key => $value) {
    if (strpos($key, 'filter_') === 0 && !empty($value)) {
        $attribute = str_replace('filter_', '', $key);
        $value = wc_clean(wp_unslash($value));
        $active_filters[] = sprintf('%s: %s', ucfirst($attribute), esc_html($value));
    }
}

// Display active filters
if (!empty($active_filters)) {
    foreach ($active_filters as $filter) {
        echo '<span class="active-filter-tag">' . $filter . '</span>';
    }
    echo '<button class="clear-all-filters" onclick="clearAllFilters()">' . esc_html__('Alles wissen', 'textdomain') . '</button>';
}

echo '</div></div>';


// Categories section
echo '<div class="filter-section categories-section">';
echo '<h4 class="filter-title">' . esc_html__('Categorie', 'textdomain') . '</h4>';
the_widget(
    'WC_Widget_Product_Categories',
    array(
        'title'              => '',
        'orderby'            => 'name',
        'hierarchical'       => 1,
        'count'              => 1,
        'dropdown'           => 0,
        'show_children_only' => false,
        'hide_empty'         => 1,
    ),
    array(
        'before_widget' => '<div class="widget widget_product_categories">',
        'after_widget'  => '</div>',
        'before_title'  => '<h5 class="widget-title">',
        'after_title'   => '</h5>',
    )
);
echo '</div>';

// Price filter section
echo '<div class="filter-section price-section">';
echo '<h4 class="filter-title">' . esc_html__('Prijs', 'textdomain') . '</h4>';
the_widget('WC_Widget_Price_Filter', array(
    'title' => ''
), array(
    'before_widget' => '<div class="widget widget_price_filter">',
    'after_widget'  => '</div>',
    'before_title'  => '<h5 class="widget-title">',
    'after_title'   => '</h5>',
));
echo '</div>';

// Stock section
echo '<div class="filter-section stock-section">';
echo '<h4 class="filter-title">' . esc_html__('Beschikbaarheid', 'textdomain') . '</h4>';
echo '<div class="stock-filters">';

$stock_status = isset($_GET['stock_status']) ? wc_clean(wp_unslash($_GET['stock_status'])) : '';
$stock_values = $stock_status ? explode(',', $stock_status) : array();

echo '<label class="filter-checkbox"><input type="checkbox" name="stock_status" value="instock"' . (in_array('instock', $stock_values) ? ' checked' : '') . '> ' . esc_html__('Op voorraad', 'textdomain') . '</label>';
echo '<label class="filter-checkbox"><input type="checkbox" name="stock_status" value="outofstock"' . (in_array('outofstock', $stock_values) ? ' checked' : '') . '> ' . esc_html__('Uitverkocht', 'textdomain') . '</label>';
echo '<label class="filter-checkbox"><input type="checkbox" name="stock_status" value="onbackorder"' . (in_array('onbackorder', $stock_values) ? ' checked' : '') . '> ' . esc_html__('Op bestelling', 'textdomain') . '</label>';

echo '</div>';
echo '</div>';

// Attributes sections
$attribute_taxonomies = wc_get_attribute_taxonomies();
if ($attribute_taxonomies) {
    foreach ($attribute_taxonomies as $attribute) {
        $taxonomy = 'pa_' . $attribute->attribute_name;
        $terms = get_terms(array(
            'taxonomy' => $taxonomy,
            'hide_empty' => true,
        ));

        if (!empty($terms) && !is_wp_error($terms)) {
            echo '<div class="filter-section attribute-section">';
            echo '<h4 class="filter-title">' . esc_html($attribute->attribute_label) . '</h4>';
            echo '<div class="attribute-filters">';

            $filter_key = 'filter_' . $attribute->attribute_name;
            $selected_values = isset($_GET[$filter_key]) ? explode(',', wc_clean(wp_unslash($_GET[$filter_key]))) : array();

            foreach ($terms as $term) {
                $checked = in_array($term->slug, $selected_values) ? ' checked' : '';
                echo '<label class="filter-checkbox">';
                echo '<input type="checkbox" name="' . esc_attr($filter_key) . '[]" value="' . esc_attr($term->slug) . '"' . $checked . '>';
                echo esc_html($term->name) . ' (' . absint($term->count) . ')';
                echo '</label>';
            }
            echo '</div>';
            echo '</div>';
        }
    }
}

echo '</div>'; // .sidebar-content
echo '</aside>';

// Main content area
echo '<main class="custom-shop-main">';

// Breadcrumbs
echo '<div class="shop-breadcrumbs">';
woocommerce_breadcrumb(array(
    'delimiter'   => ' / ',
    'wrap_before' => '<nav class="woocommerce-breadcrumb breadcrumb-nav">',
    'wrap_after'  => '</nav>',
    'before'      => '',
    'after'       => '',
    'home'        => __('Home', 'textdomain'),
));
echo '</div>';

// Shop header with title and controls
echo '<div class="shop-header">';
echo '<div class="shop-title-section">';

if ($is_category) {
    echo '<h1 class="shop-title">' . single_cat_title('', false) . '</h1>';
    if ($current_cat->description) {
        echo '<div class="category-description">' . wpautop($current_cat->description) . '</div>';
    }
} else {
    echo '<h1 class="shop-title">' . esc_html(get_the_title($shop_page_id)) . '</h1>';
}

echo '</div>';

// Results info and sorting
echo '<div class="shop-controls">';
echo '<div class="results-info">';
if (woocommerce_product_loop()) {
    global $wp_query;
    $total = $wp_query->found_posts;
    $current_page = max(1, get_query_var('paged'));
    $per_page = get_query_var('posts_per_page');
    $first = ($current_page - 1) * $per_page + 1;
    $last = min($current_page * $per_page, $total);

    echo '<span class="results-count">';
    printf(
        esc_html__('Toont %d-%d van %d resultaten', 'textdomain'),
        $first,
        $last,
        $total
    );
    echo '</span>';
}
echo '</div>';

// View mode toggle
echo '<div class="view-controls">';
echo '<span class="view-label">' . esc_html__('Weergave:', 'textdomain') . '</span>';
echo '<button class="view-toggle grid-view active" data-view="grid" aria-label="' . esc_attr__('Rasterweergave', 'textdomain') . '">⊞</button>';
echo '<button class="view-toggle list-view" data-view="list" aria-label="' . esc_attr__('Lijstweergave', 'textdomain') . '">☰</button>';
echo '</div>';

// Sort dropdown
echo '<div class="sort-controls">';
woocommerce_catalog_ordering();
echo '</div>';
echo '</div>'; // .shop-controls
echo '</div>'; // .shop-header

// Products grid/list
if (woocommerce_product_loop()) {
    echo '<div class="products-container">';
    echo '<ul class="custom-product-grid" id="products-grid">';

    while (have_posts()) {
        the_post();
        global $product;

        if (!$product || !$product->is_visible()) {
            continue;
        }

        // Product item
        echo '<li class="custom-product-item" data-product-id="' . esc_attr($product->get_id()) . '">';

        // Product image with badges
        echo '<div class="product-image-container">';
        echo '<a href="' . esc_url(get_the_permalink()) . '" class="product-image-link">';

        // Product badges
        echo '<div class="product-badges">';

        // Discount badge
        if ($product->is_on_sale()) {
            $regular_price = $product->get_regular_price();
            $sale_price = $product->get_sale_price();

            if ($regular_price && $sale_price) {
                $discount_percentage = round((($regular_price - $sale_price) / $regular_price) * 100);
                if ($discount_percentage > 0) {
                    echo '<span class="badge discount-badge">-' . absint($discount_percentage) . '%</span>';
                }
            } else {
                echo '<span class="badge discount-badge">' . esc_html__('Aanbieding', 'textdomain') . '</span>';
            }
        }

        // Other badges
        if ($product->is_featured()) {
            echo '<span class="badge featured-badge">' . esc_html__('Uitgelicht', 'textdomain') . '</span>';
        }

        $created_date = strtotime($product->get_date_created());
        if ($created_date > strtotime('-30 days')) {
            echo '<span class="badge new-badge">' . esc_html__('Nieuw', 'textdomain') . '</span>';
        }

        if (!$product->is_in_stock()) {
            echo '<span class="badge out-of-stock-badge">' . esc_html__('Uitverkocht', 'textdomain') . '</span>';
        }

        echo '</div>';

        // Product image
        echo woocommerce_get_product_thumbnail('woocommerce_thumbnail');
        echo '</a>';

        echo '</div>'; // .product-image-container

        // Product info
        echo '<div class="product-info">';

        // Product title
        echo '<h3 class="product-title">';
        echo '<a href="' . esc_url(get_the_permalink()) . '">' . get_the_title() . '</a>';
        echo '</h3>';

        // Product rating
        if ($product->get_average_rating()) {
            echo '<div class="product-rating">';
            echo wc_get_rating_html($product->get_average_rating());
            echo '<span class="rating-count">(' . absint($product->get_review_count()) . ')</span>';
            echo '</div>';
        }

        // Product price
        echo '<div class="product-price">';
        echo $product->get_price_html();
        echo '</div>';

        // Short description
        $short_desc = $product->get_short_description();
        if ($short_desc) {
            echo '<div class="product-excerpt">' . wp_trim_words($short_desc, 15) . '</div>';
        }

        // Add to cart button
        echo '<div class="product-cart-section">';
        if ($product->is_purchasable() && $product->is_in_stock()) {
            if ($product->is_type('simple')) {
                $nonce = wp_create_nonce('vanatex-add-to-cart-' . $product->get_id());
                echo '<button class="add-to-cart-btn ajax-add-to-cart" data-product-id="' . esc_attr($product->get_id()) . '" data-nonce="' . esc_attr($nonce) . '">';
                echo esc_html__('In winkelwagen', 'textdomain');
                echo '</button>';
            } else {
                echo '<a href="' . esc_url(get_the_permalink()) . '" class="view-product-btn">' . esc_html__('Bekijk product', 'textdomain') . '</a>';
            }
        } else {
            echo '<span class="out-of-stock-text">' . esc_html__('Niet beschikbaar', 'textdomain') . '</span>';
        }
        echo '</div>';

        echo '</div>'; // .product-info
        echo '</li>';
    }

    echo '</ul>';
    echo '</div>'; // .products-container

    // Pagination
    echo '<div class="shop-pagination">';
    woocommerce_pagination();
    echo '</div>';

} else {
    echo '<div class="no-products-found">';
    echo '<h2>' . esc_html__('Geen producten gevonden', 'textdomain') . '</h2>';
    echo '<p>' . esc_html__('Probeer je zoekopdracht aan te passen of filters te wijzigen.', 'textdomain') . '</p>';
    echo '<button class="clear-all-filters" onclick="clearAllFilters()">' . esc_html__('Alle filters wissen', 'textdomain') . '</button>';
    echo '</div>';
}

echo '</main>';
echo '</div>'; // .custom-shop-container