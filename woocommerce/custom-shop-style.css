:root {

  /* === BREAKDANCE GLOBAL COLORS === */
  --white: #fff;
  --grey-50: #f9fafb;
  --grey-100: #f3f4f6;
  --grey-200: #e5e7eb;
  --grey-300: #d1d5db;
  --grey-400: #9ca3af;
  --grey-450: #787e8b;
  --grey-500: #6b7280;
  --grey-600: #4b5563;
  --grey-700: #374151;
  --grey-800: #1f2937;
  --grey-900: #111827;
  --blue-500: #3b82f6;
  --blue-600: #2563eb;
  --pink-600: #db2777;
  --emerald-100: #d1fae5;
  --emerald-700: #047857;
  --sky-100: #e0f2fe;
  --sky-500: #0ea5e9;
  --red-50: #fef2f2;
  --red-500: #ef4444;
  --red-700: #b91c1c;
  --indigo-50: #eef2ff;
  --indigo-600: #4f46e5;
  --indigo-700: #4338ca;
  --yellow-500: #fbbf24;
  --black: #000;

  /* === BREAKDANCE BRAND COLORS === */
  --bde-brand-primary-color: #eb1c74;
  --bde-brand-primary-color-hover: #eb1c74;

  /* === MAPPED COLORS FOR WOOCOMMERCE === */
  --primary-color: var(--bde-brand-primary-color);
  --primary-hover: var(--red-700);
  --primary-light: var(--red-50);
  --secondary-color: var(--yellow-500);
  --secondary-hover: #d68910;
  --secondary-light: #fef5e7;

  --success-color: var(--emerald-700);
  --success-light: var(--emerald-100);
  --warning-color: var(--yellow-500);
  --warning-light: #fef5e7;
  --danger-color: var(--red-500);
  --danger-light: var(--red-50);
  --info-color: var(--sky-500);
  --info-light: var(--sky-100);

  /* === NEUTRAL COLORS (MAPPED) === */
  --gray-50: var(--grey-50);
  --gray-100: var(--grey-100);
  --gray-200: var(--grey-200);
  --gray-300: var(--grey-300);
  --gray-400: var(--grey-400);
  --gray-500: var(--grey-500);
  --gray-600: var(--grey-600);
  --gray-700: var(--grey-700);
  --gray-800: var(--grey-800);
  --gray-900: var(--grey-900);

  /* === BORDER COLORS === */
  --border-color: var(--grey-200);
  --border-color-dark: var(--grey-300);

  /* === BACKGROUND COLORS === */
  --background-primary: var(--white);
  --background-secondary: var(--grey-50);
  --background-muted: var(--grey-100);

  /* === TEXT COLORS === */
  --text-primary: var(--grey-900);
  --text-secondary: var(--grey-700);
  --text-muted: var(--grey-500);
  --text-light: var(--grey-400);

  /* === TYPOGRAPHY === */
  --font-family-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-family-secondary: 'Poppins', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', monospace;

  --font-size-xs: 0.75rem;    /* 12px */
  --font-size-sm: 0.875rem;   /* 14px */
  --font-size-base: 1rem;     /* 16px */
  --font-size-lg: 1.125rem;   /* 18px */
  --font-size-xl: 1.25rem;    /* 20px */
  --font-size-2xl: 1.5rem;    /* 24px */
  --font-size-3xl: 1.875rem;  /* 30px */
  --font-size-4xl: 2.25rem;   /* 36px */
  --font-size-5xl: 3rem;      /* 48px */

  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;

  --line-height-tight: 1.25;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.75;

  /* === SPACING === */
  --space-xs: 0.25rem;   /* 4px */
  --space-sm: 0.5rem;    /* 8px */
  --space-md: 0.75rem;   /* 12px */
  --space-base: 1rem;    /* 16px */
  --space-lg: 1.25rem;   /* 20px */
  --space-xl: 1.5rem;    /* 24px */
  --space-2xl: 2rem;     /* 32px */
  --space-3xl: 2.5rem;   /* 40px */
  --space-4xl: 3rem;     /* 48px */
  --space-5xl: 4rem;     /* 64px */
  --space-6xl: 5rem;     /* 80px */

  /* === LAYOUT === */
  --container-max-width: 75rem;   /* 1200px */
  --sidebar-width: 18.75rem;      /* 300px */
  --sidebar-width-mobile: 100%;
  --header-height: 4rem;          /* 64px */

  /* === BORDERS & RADIUS === */
  --border-width: 0.0625rem;     /* 1px */
  --border-width-thick: 0.125rem; /* 2px */
  --border-color: var(--gray-200);
  --border-color-dark: var(--gray-300);

  --border-radius-sm: 0.25rem;    /* 4px */
  --border-radius-base: 0.375rem; /* 6px */
  --border-radius-md: 0.5rem;     /* 8px */
  --border-radius-lg: 0.75rem;    /* 12px */
  --border-radius-xl: 1rem;       /* 16px */
  --border-radius-full: 9999px;

  /* === SHADOWS === */
  --shadow-sm: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
  --shadow-base: 0 0.0625rem 0.1875rem rgba(0, 0, 0, 0.1), 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.06);
  --shadow-md: 0 0.25rem 0.375rem -0.0625rem rgba(0, 0, 0, 0.1), 0 0.125rem 0.25rem -0.0625rem rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 0.625rem 0.9375rem -0.1875rem rgba(0, 0, 0, 0.1), 0 0.25rem 0.375rem -0.125rem rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 1.25rem 1.5625rem -0.1875rem rgba(0, 0, 0, 0.1), 0 0.625rem 0.625rem -0.25rem rgba(0, 0, 0, 0.04);

  /* === TRANSITIONS === */
  --transition-fast: 0.15s ease-in-out;
  --transition-base: 0.2s ease-in-out;
  --transition-slow: 0.3s ease-in-out;

  /* === Z-INDEX === */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
}

/* === RESET & BASE STYLES === */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-primary);
  font-size: var(--font-size-base);
  line-height: var(--line-height-normal);
  color: var(--gray-800);
  background-color: var(--gray-50);
  margin: 0;
  padding: 0;
}

/* === MAIN SHOP CONTAINER === */
.custom-shop-container {
  display: grid;
  grid-template-columns: var(--sidebar-width) 1fr;
  gap: var(--space-2xl);
  margin: 0 auto;
  padding: var(--space-xl);
  min-height: 100vh;
  position: relative;
}

/* === MOBILE FILTER TOGGLE === */
.mobile-filter-toggle {
  display: none;
  position: fixed;
  bottom: var(--space-xl);
  right: var(--space-xl);
  background: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: var(--border-radius-full);
  padding: var(--space-base);
  box-shadow: var(--shadow-lg);
  z-index: var(--z-fixed);
  cursor: pointer;
  transition: all var(--transition-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  align-items: center;
  gap: var(--space-sm);
}

.mobile-filter-toggle:hover {
  background: var(--primary-hover);
  transform: translateY(-0.125rem);
}

.filter-icon {
  font-size: var(--font-size-lg);
}

/* === SIDEBAR STYLES === */
/* === MINIMALISTIC SIDEBAR OVERRIDE === */
.custom-shop-sidebar {
  background: transparent;
  border-radius: 0;
  padding: 0;
  box-shadow: none;
  border: none;
}

.sidebar-header {
  display: none;
}

.sidebar-content {
  padding: 0;
}

/* === ACTIVE FILTERS SECTION === */
.active-filters-section {
  margin-bottom: var(--space-xl);
  padding: 0;
}

.active-filters-section h4 {
  display: none;
}

.active-filters-list {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-xs);
  margin-bottom: var(--space-base);
}

.active-filter-tag {
  background: var(--gray-100);
  color: var(--gray-700);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  border: var(--border-width) solid var(--gray-200);
  position: relative;
  padding-right: var(--space-xl);
}

.active-filter-tag::after {
  content: '×';
  position: absolute;
  right: var(--space-xs);
  top: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  color: var(--gray-500);
  font-weight: var(--font-weight-bold);
}

.active-filter-tag:hover {
  background: var(--gray-200);
}

.clear-all-filters {
  background: transparent;
  color: var(--gray-600);
  border: var(--border-width) solid var(--gray-300);
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-normal);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-transform: none;
}

.clear-all-filters:hover {
  background: var(--gray-100);
  opacity: 1;
}

/* === COLLAPSIBLE FILTER SECTIONS === */
.filter-section {
  margin-bottom: var(--space-base);
  padding-bottom: 0;
  border-bottom: var(--border-width) solid var(--gray-200);
}

.filter-section:last-child {
  border-bottom: var(--border-width) solid var(--gray-200);
  margin-bottom: var(--space-base);
}

.filter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-sm) 0;
  cursor: pointer;
  user-select: none;
  transition: color var(--transition-fast);
}

.filter-header:hover {
  color: var(--primary-color);
}

.custom-shop-container .filter-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--gray-900);
  margin: 0;
  text-transform: none;
}

.filter-toggle {
  font-size: var(--font-size-lg);
  color: var(--gray-500);
  transition: transform var(--transition-fast);
  line-height: 1;
}

.filter-section.collapsed .filter-toggle {
  transform: rotate(-90deg);
}

.filter-content {
  overflow: hidden;
  transition: max-height var(--transition-base);
  max-height: 31.25rem; /* 500px */
}

.filter-section.collapsed .filter-content {
  max-height: 0;
}

/* === SEARCH SECTION MINIMAL === */
.search-section {
  margin-bottom: var(--space-xl);
}

.search-section h4 {
  display: none;
}

.product-search-form {
  border: var(--border-width) solid var(--gray-300);
  border-radius: var(--border-radius-sm);
  background: var(--white);
}

.product-search-input {
  font-size: var(--font-size-sm);
  color: var(--gray-700);
}

.product-search-input::placeholder {
  color: var(--gray-400);
}

.search-button {
  background: var(--gray-100);
  color: var(--gray-600);
  border-left: var(--border-width) solid var(--gray-300);
}

.search-button:hover {
  background: var(--gray-200);
  color: var(--gray-800);
}

/* === MINIMAL FILTER CHECKBOXES === */
.filter-checkbox {
  display: block;
  margin-bottom: var(--space-xs);
  padding: var(--space-xs) 0;
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  font-weight: var(--font-weight-normal);
}

.filter-checkbox:hover {
  color: var(--gray-900);
}

.filter-checkbox input[type="checkbox"] {
  width: 0.875rem;
  height: 0.875rem;
  margin-right: var(--space-sm);
}

/* === STOCK FILTERS === */
.stock-filters {
  padding: var(--space-sm) 0;
}

/* === ATTRIBUTE FILTERS === */
.attribute-filters {
  padding: var(--space-sm) 0;
}

/* === RATING FILTERS === */
.rating-filters {
  padding: var(--space-sm) 0;
}

.rating-filter .stars-display {
  color: var(--gray-400);
  font-size: var(--font-size-sm);
  margin-left: 0;
  margin-right: var(--space-xs);
}

/* === CATEGORIES WIDGET MINIMAL === */
.widget_product_categories {
  padding: var(--space-sm) 0;
}

.widget_product_categories ul {
  margin: 0;
  padding: 0;
}

.widget_product_categories li {
  margin-bottom: var(--space-xs);
}

.widget_product_categories a {
  padding: var(--space-xs) 0;
  font-size: var(--font-size-sm) !important;
  color: var(--gray-700) !important;
  font-weight: var(--font-weight-normal) !important;
  text-decoration: none !important;
  border-radius: 0;
  background: transparent;

}

.widget_product_categories a:hover {
  background: transparent;
  color: var(--primary-color);
}

.widget_product_categories .current-cat > a {
  background: transparent;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.widget_product_categories .count {
  background: transparent;
  color: var(--gray-500);
  padding: 0;
  border-radius: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
}

/* Hide subcategories by default */
.widget_product_categories li li {
  display: none;
}

/* Show subcategories only for current category */
.widget_product_categories .current-cat-parent > ul,
.widget_product_categories .current-cat > ul {
  display: block;
}

.widget_product_categories .current-cat-parent li li,
.widget_product_categories .current-cat li li {
  display: list-item;
  margin-left: var(--space-base);
  padding-left: var(--space-sm);
  border-left: var(--border-width) solid var(--gray-200);
}

/* === PRICE FILTER MINIMAL === */
.widget_price_filter {
  padding: var(--space-sm) 0;
}

.widget_price_filter .price_slider_wrapper {
  margin: var(--space-base) 0;
}

.widget_price_filter .price_slider {
  background: var(--gray-200) !important;
  height: 0.1875rem !important;
  border-radius: var(--border-radius-sm) !important;
  position: relative !important;
  margin: var(--space-base) 0 !important;
}

.widget_price_filter .ui-slider-range {
  background: var(--primary-color) !important;
  height: 100% !important;
  border-radius: var(--border-radius-sm) !important;
  position: absolute !important;
  top: 0 !important;
}

.widget_price_filter .ui-slider-handle {
  background: var(--primary-color) !important;
  border: var(--border-width-thick) solid var(--white) !important;
  box-shadow: var(--shadow-sm) !important;
  width: 0.875rem !important;
  height: 0.875rem !important;
  border-radius: 50% !important;
  cursor: pointer !important;
  position: absolute !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  margin-left: -0.4375rem !important; /* Half of width for centering */
  outline: none !important;
  z-index: 2 !important;
}

.widget_price_filter .ui-slider-handle:hover {
  background: var(--primary-hover) !important;
  transform: translateY(-50%) scale(1.1) !important;
}

.widget_price_filter .ui-slider-handle:focus {
  box-shadow: 0 0 0 0.1875rem var(--primary-light) !important;
}

.widget_price_filter .price_slider_amount {
  margin-top: var(--space-base);
}

.widget_price_filter .price_label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
}

.widget_price_filter button {
  background: var(--primary-color);
  padding: var(--space-xs) var(--space-base);
  font-size: var(--font-size-sm);
  border-radius: var(--border-radius-sm);
}

/* === MOBILE ADJUSTMENTS === */
@media (max-width: 48rem) {
  .custom-shop-sidebar {
    background: var(--white);
    border-radius: 0;
    padding: var(--space-xl);
  }

  .sidebar-header {
    display: flex;
  }

  .filter-section {
    border-bottom: var(--border-width) solid var(--gray-200);
  }
}

/* === MAIN CONTENT === */
.custom-shop-main {
  /*padding: var(--space-xl);*/
}

/* === BREADCRUMBS === */
.shop-breadcrumbs {
  margin-bottom: var(--space-xl);
}

.breadcrumb-nav {
  font-size: var(--font-size-sm);
  color: var(--gray-500);
}

.breadcrumb-nav a {
  color: var(--primary-color);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.breadcrumb-nav a:hover {
  color: var(--primary-hover);
  text-decoration: underline;
}

/* === SHOP HEADER === */
.shop-header {
  margin-bottom: var(--space-2xl);
  padding-bottom: var(--space-xl);
  border-bottom: var(--border-width) solid var(--border-color);
}

.shop-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--gray-900);
  margin: 0 0 var(--space-base) 0;
  line-height: var(--line-height-tight);
}

.category-description {
  color: var(--gray-600);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-xl);
}

/* === SHOP CONTROLS === */
.shop-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-base);
  margin-top: var(--space-xl);
}

.results-info {
  flex: 1;
}

.results-count {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

/* === VIEW CONTROLS === */
.view-controls {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
}

.view-label {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  font-weight: var(--font-weight-medium);
}

.view-toggle {
  background: var(--gray-100);
  border: var(--border-width) solid var(--border-color);
  padding: var(--space-sm);
  border-radius: var(--border-radius-base);
  cursor: pointer;
  transition: all var(--transition-fast);
  font-size: var(--font-size-base);
  color: var(--gray-600);
}

.view-toggle:hover {
  background: var(--gray-200);
  color: var(--gray-800);
}

.view-toggle.active {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* === SORT CONTROLS === */
.sort-controls select {
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  padding: var(--space-sm) var(--space-base);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-sm);
  color: var(--gray-700);
  cursor: pointer;
  transition: border-color var(--transition-fast);
}

.sort-controls select:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 0.1875rem var(--primary-light);
}

/* === PRODUCTS GRID === */
.products-container {
  margin-top: var(--space-2xl);
}

.custom-product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(15.625rem, 1fr));
  gap: var(--space-xl);
  list-style: none;
  padding: 0;
  margin: 0;
}

.custom-product-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-xl);
  list-style: none;
  padding: 0;
  margin: 0;
}

/* === PRODUCT ITEM === */
.custom-product-item {
  background: var(--white);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  transition: all var(--transition-base);
  position: relative;
  display: flex;
  flex-direction: column;
}

.custom-product-item:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-0.125rem);
  border-color: var(--primary-color);
}

.custom-product-list .custom-product-item {
  flex-direction: row;
  align-items: center;
}

/* === PRODUCT IMAGE === */
.product-image-container {
  position: relative;
  overflow: hidden;
  aspect-ratio: 1/1;
}

.custom-product-list .product-image-container {
  width: 12.5rem;
  flex-shrink: 0;
  aspect-ratio: 1/1;
}

.product-image-link {
  display: block;
  width: 100%;
  height: 100%;
}

.product-image-link img {
  width: 100%;
  height: 100%;
  aspect-ratio: 1/1;
  object-fit: contain;
  transition: transform var(--transition-base);
}

.custom-product-item:hover .product-image-link img {
  transform: scale(1.05);
}

/* === PRODUCT BADGES === */
.product-badges {
  position: absolute;
  top: var(--space-sm);
  left: var(--space-sm);
  z-index: 10;
  display: flex;
  flex-direction: column;
  gap: var(--space-xs);
}

.badge {
  padding: var(--space-xs) var(--space-sm);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.025rem;
}

.sale-badge {
  background: var(--danger-color);
  color: var(--white);
}

.featured-badge {
  background: var(--warning-color);
  color: var(--white);
}

.new-badge {
  background: var(--success-color);
  color: var(--white);
}

.out-of-stock-badge {
  background: var(--gray-500);
  color: var(--white);
}

.discount-badge {
  background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%);
  color: var(--white);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.3);
  border: 0.125rem solid var(--white);
  box-shadow: var(--shadow-sm);
}

.discount-badge::before {
  content: '🔥';
  margin-right: var(--space-xs);
}


/* === PRODUCT INFO === */
.product-info {
  padding: var(--space-base);
  flex: 1;
  display: flex;
  flex-direction: column;
}

.custom-product-item .product-info {
  padding: var(--space-xl);
}

.custom-product-item .product-info h3.product-title {
  margin: 0 0 var(--space-sm) 0;
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
}

.custom-product-item .product-info .product-title a {
  color: var(--gray-800);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.custom-product-item .product-info .product-title a:hover {
  color: var(--primary-color);
}

/* === PRODUCT RATING === */
.product-rating {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  margin-bottom: var(--space-sm);
}

.product-rating .star-rating {
  color: var(--warning-color);
  font-size: var(--font-size-sm);
}

.rating-count {
  font-size: var(--font-size-xs);
  color: var(--gray-500);
}

/* === PRODUCT PRICE === */
.product-price {
  margin-bottom: var(--space-sm);
  font-weight: var(--font-weight-semibold);
}

.product-price .price {
  font-size: var(--font-size-lg);
  color: var(--gray-900);
}

.product-price .price del {
  color: var(--gray-400);
  font-weight: var(--font-weight-normal);
  font-size: var(--font-size-base);
  margin-right: var(--space-xs);
}

.product-price .price ins {
  color: var(--danger-color);
  text-decoration: none;
}

/* === PRODUCT EXCERPT === */
.product-excerpt {
  font-size: var(--font-size-sm);
  color: var(--gray-600);
  line-height: var(--line-height-relaxed);
  margin-bottom: var(--space-base);
  flex: 1;
}

.custom-product-grid .product-excerpt {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* === PRODUCT CART SECTION === */
.product-cart-section {
  margin-top: auto;
}

.custom-product-item .add-to-cart-btn,
.custom-product-item .view-product-btn {
  width: 100%;
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--space-sm) var(--space-base);
  border-radius: var(--border-radius-base);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  text-decoration: none;
  text-align: center;
  display: block;
}

.add-to-cart-btn:hover,
.view-product-btn:hover {
  background: var(--primary-hover);
  transform: translateY(-0.0625rem);
}

.add-to-cart-btn.loading {
  opacity: 0.7;
  cursor: not-allowed;
}

.add-to-cart-btn.added {
  background: var(--success-color);
}

.out-of-stock-text {
  color: var(--gray-500);
  font-size: var(--font-size-sm);
  font-style: italic;
  text-align: center;
  padding: var(--space-sm);
}

/* === NO PRODUCTS FOUND === */
.no-products-found {
  text-align: center;
  padding: var(--space-5xl) var(--space-xl);
  color: var(--gray-600);
}

.no-products-found h2 {
  font-size: var(--font-size-2xl);
  color: var(--gray-800);
  margin-bottom: var(--space-base);
}

.no-products-found p {
  font-size: var(--font-size-lg);
  margin-bottom: var(--space-xl);
}

/* === PAGINATION === */
/* === SUBTLE PAGINATION STYLES === */
.shop-pagination {
  margin-top: var(--space-5xl);
  display: flex;
  justify-content: center;
}

.shop-pagination .page-numbers {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  list-style: none;
}

.shop-pagination .page-numbers a,
.shop-pagination .page-numbers span {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-sm) var(--space-base);
  border: var(--border-width) solid var(--border-color);
  border-radius: var(--border-radius-base);
  color: var(--text-secondary);
  text-decoration: none;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  transition: all var(--transition-fast);
  min-width: 2.5rem;
  min-height: 2.5rem;
  text-align: center;
  background: var(--white);
}

/* Hover state - subtle primary color */
.shop-pagination .page-numbers a:hover {
  background: var(--gray-50);
  color: var(--text-primary);
  border-color: var(--gray-300);
  transform: translateY(-0.0625rem);
  box-shadow: var(--shadow-sm);
}

/* Current/Active page - subtle accent */
.shop-pagination .page-numbers .current {
  background: var(--gray-100);
  color: var(--text-primary);
  border-color: var(--gray-300);
  font-weight: var(--font-weight-semibold);
  box-shadow: inset 0 0.0625rem 0.125rem rgba(0, 0, 0, 0.05);
}

/* Previous/Next arrows */
.shop-pagination .page-numbers .prev,
.shop-pagination .page-numbers .next {
  font-weight: var(--font-weight-normal);
}

.shop-pagination .page-numbers .prev:hover,
.shop-pagination .page-numbers .next:hover {
  background: var(--primary-color);
  color: var(--white);
  border-color: var(--primary-color);
}

/* Dots (...) styling */
.shop-pagination .page-numbers .dots {
  background: transparent;
  border: none;
  color: var(--text-muted);
  cursor: default;
  font-weight: var(--font-weight-bold);
}

.shop-pagination .page-numbers .dots:hover {
  background: transparent;
  color: var(--text-muted);
  border: none;
  transform: none;
  box-shadow: none;
}

/* Disabled state */
.shop-pagination .page-numbers .disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: var(--gray-50);
  color: var(--text-light);
}

.shop-pagination .page-numbers .disabled:hover {
  background: var(--gray-50);
  color: var(--text-light);
  border-color: var(--border-color);
  transform: none;
  box-shadow: none;
}

/* Mobile responsive pagination */
@media (max-width: 48rem) {
  .shop-pagination .page-numbers {
    gap: 0.25rem;
  }

  .shop-pagination .page-numbers a,
  .shop-pagination .page-numbers span {
    min-width: 2.25rem;
    min-height: 2.25rem;
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--font-size-xs);
  }

  /* Hide some page numbers on mobile */
  .shop-pagination .page-numbers a:not(.prev):not(.next):not(.current):nth-child(n+6) {
    display: none;
  }
}

@media (max-width: 30rem) {
  .shop-pagination .page-numbers a,
  .shop-pagination .page-numbers span {
    min-width: 2rem;
    min-height: 2rem;
    padding: var(--space-xs);
  }
}

/* === RESPONSIVE DESIGN === */
@media (max-width: 75rem) {
  .custom-shop-container {
    max-width: 100%;
    padding: var(--space-base);
    gap: var(--space-xl);
  }
}

@media (max-width: 64rem) {
  :root {
    --sidebar-width: 16.25rem;
  }

  .custom-product-grid {
    grid-template-columns: repeat(auto-fill, minmax(13.75rem, 1fr));
    gap: var(--space-lg);
  }

  .shop-controls {
    flex-direction: column;
    align-items: stretch;
    gap: var(--space-base);
  }

  .view-controls,
  .sort-controls {
    justify-content: center;
  }
}

@media (max-width: 48rem) {
  .custom-shop-container {
    grid-template-columns: 1fr;
    gap: 0;
    padding: var(--space-sm);
  }

  .mobile-filter-toggle {
    display: flex;
  }

  .custom-shop-sidebar {
    position: fixed;
    top: 0;
    left: 0;
    width: var(--sidebar-width-mobile);
    height: 100vh;
    z-index: var(--z-modal);
    transform: translateX(-100%);
    transition: transform var(--transition-base);
    border-radius: 0;
    max-height: none;
  }

  .custom-shop-sidebar.active {
    transform: translateX(0);
  }

  .sidebar-header .close-sidebar {
    display: block;
  }

  .custom-shop-main {
    margin-top: 0;
    border-radius: var(--border-radius-base);
    padding: var(--space-base);
  }

  .shop-title {
    font-size: var(--font-size-2xl);
  }

  .custom-product-grid {
    grid-template-columns: repeat(auto-fill, minmax(10rem, 1fr));
    gap: var(--space-base);
  }

  .custom-product-list .custom-product-item {
    flex-direction: column;
  }

  .custom-product-list .product-image-container {
    width: 100%;
    aspect-ratio: 1;
  }

  .custom-product-list .product-info {
    padding: var(--space-base);
  }

  body.sidebar-open {
    overflow: hidden;
  }

  body.sidebar-open::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: calc(var(--z-modal) - 1);
  }
}

@media (max-width: 30rem) {
  .custom-product-grid {
    grid-template-columns: repeat(auto-fill, minmax(8.75rem, 1fr));
    gap: var(--space-sm);
  }

  .product-info {
    padding: var(--space-sm);
  }

  .shop-header {
    margin-bottom: var(--space-base);
    padding-bottom: var(--space-base);
  }

  .shop-title {
    font-size: var(--font-size-xl);
  }
}

/* === LOADING STATES === */
.loading-skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* === ACCESSIBILITY === */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

.sr-only {
  position: absolute;
  width: 0.0625rem;
  height: 0.0625rem;
  padding: 0;
  margin: -0.0625rem;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* === FOCUS STYLES === */
button:focus-visible,
input:focus-visible,
select:focus-visible,
a:focus-visible {
  outline: 0.125rem solid var(--primary-color);
  outline-offset: 0.125rem;
}

/* === PRINT STYLES === */
@media print {
  .mobile-filter-toggle,
  .custom-shop-sidebar,
  .product-actions,
  .shop-pagination {
    display: none !important;
  }

  .custom-shop-container {
    grid-template-columns: 1fr;
  }

  .custom-product-item {
    break-inside: avoid;
  }
}