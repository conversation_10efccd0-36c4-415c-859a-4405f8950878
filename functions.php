<?php
/**
 * Breakdance Zero Theme - Child Theme Functions
 */

// Exit if accessed directly
if (!defined('ABSPATH')) {
    exit;
}

// CSS van shop inladen
function vanatex_enqueue_custom_shop_style() {
    if (is_shop() || is_product_category()) {
        wp_enqueue_style(
            'vanatex-custom-shop-style',
            get_stylesheet_directory_uri() . '/woocommerce/custom-shop-style.css',
            array(),
            filemtime(get_stylesheet_directory() . '/woocommerce/custom-shop-style.css')
        );
    }
}
add_action('wp_enqueue_scripts', 'vanatex_enqueue_custom_shop_style');

// Alleen categorieën met producten tonen
add_filter('woocommerce_product_categories_widget_args', function($args) {
    $args['hide_empty'] = true;
    return $args;
});

/**
 * WooCommerce Shop Filter Backend Logic
 */
add_action('woocommerce_product_query', 'vanatex_custom_woocommerce_product_query_filters');

function vanatex_custom_woocommerce_product_query_filters($query) {
    // Only apply on shop pages and not in admin
    if (!is_admin() && (is_shop() || is_product_category() || is_product_tag())) {

        // Stock status filter
        if (isset($_GET['stock_status']) && !empty($_GET['stock_status'])) {
            $stock_statuses = explode(',', sanitize_text_field(wp_unslash($_GET['stock_status'])));
            $meta_query = $query->get('meta_query') ?: array();

            $stock_meta_query = array(
                'relation' => 'OR'
            );

            foreach ($stock_statuses as $status) {
                $stock_meta_query[] = array(
                    'key'     => '_stock_status',
                    'value'   => sanitize_text_field($status),
                    'compare' => '='
                );
            }

            $meta_query[] = $stock_meta_query;
            $query->set('meta_query', $meta_query);
        }

        // Rating filter
        if (isset($_GET['rating_filter']) && !empty($_GET['rating_filter'])) {
            $ratings = explode(',', sanitize_text_field(wp_unslash($_GET['rating_filter'])));
            $meta_query = $query->get('meta_query') ?: array();

            $rating_meta_query = array(
                'relation' => 'OR'
            );

            foreach ($ratings as $rating) {
                $rating = intval($rating);
                $rating_meta_query[] = array(
                    'key'     => '_wc_average_rating',
                    'value'   => array($rating - 0.5, $rating + 0.5),
                    'compare' => 'BETWEEN',
                    'type'    => 'DECIMAL'
                );
            }

            $meta_query[] = $rating_meta_query;
            $query->set('meta_query', $meta_query);
        }

        // Attribute filters
        $tax_query = $query->get('tax_query') ?: array();

        foreach ($_GET as $key => $value) {
            if (strpos($key, 'filter_') === 0 && !empty($value)) {
                $attribute = str_replace('filter_', '', $key);
                $values = explode(',', sanitize_text_field(wp_unslash($value)));

                $tax_query[] = array(
                    'taxonomy' => 'pa_' . $attribute,
                    'field'    => 'slug',
                    'terms'    => array_map('sanitize_text_field', $values),
                    'operator' => 'IN'
                );
            }
        }

        if (!empty($tax_query)) {
            $tax_query['relation'] = 'AND';
            $query->set('tax_query', $tax_query);
        }

        // Price range filter
        if (isset($_GET['min_price']) || isset($_GET['max_price'])) {
            $meta_query = $query->get('meta_query') ?: array();

            $price_meta_query = array(
                'key'     => '_price',
                'type'    => 'NUMERIC',
                'compare' => 'BETWEEN'
            );

            $min_price = isset($_GET['min_price']) ? floatval(wp_unslash($_GET['min_price'])) : 0;
            $max_price = isset($_GET['max_price']) ? floatval(wp_unslash($_GET['max_price'])) : PHP_INT_MAX;

            $price_meta_query['value'] = array($min_price, $max_price);

            $meta_query[] = $price_meta_query;
            $query->set('meta_query', $meta_query);
        }
    }
}

// AJAX handler for filter updates
add_action('wp_ajax_vanatex_filter_products', 'vanatex_handle_product_filter_ajax');
add_action('wp_ajax_nopriv_vanatex_filter_products', 'vanatex_handle_product_filter_ajax');

function vanatex_handle_product_filter_ajax() {
    // Verify nonce for security
    if (!isset($_POST['nonce']) || !wp_verify_nonce($_POST['nonce'], 'vanatex_product_filter_nonce')) {
        wp_send_json_error(__('Security check failed', 'textdomain'));
        wp_die();
    }

    // Return error - full implementation would go here
    wp_send_json_error(__('AJAX filtering not fully implemented', 'textdomain'));
}

// Enqueue the JavaScript file
add_action('wp_enqueue_scripts', 'vanatex_enqueue_shop_filter_scripts');

function vanatex_enqueue_shop_filter_scripts() {
    if (is_shop() || is_product_category()) {
        wp_enqueue_script(
            'vanatex-shop-filters',
            get_stylesheet_directory_uri() . '/js/shop-filters.js',
            array('jquery'),
            filemtime(get_stylesheet_directory() . '/js/shop-filters.js'),
            true
        );

        wp_localize_script('vanatex-shop-filters', 'vanatexShopFilters', array(
            'ajaxUrl' => admin_url('admin-ajax.php'),
            'nonce'   => wp_create_nonce('vanatex_product_filter_nonce'),
            'i18n'    => array(
                'loadingText' => __('Filters toepassen...', 'textdomain'),
                'errorText'   => __('Er is een fout opgetreden. Probeer het opnieuw.', 'textdomain'),
                'addedText'   => __('Toegevoegd!', 'textdomain'),
                'addingText'  => __('Bezig...', 'textdomain'),
                'addToCart'   => __('In winkelwagen', 'textdomain'),
            )
        ));
    }
}

// Force WooCommerce to use our templates
add_filter('template_include', 'vanatex_force_shop_template', 99);

function vanatex_force_shop_template($template) {
    if (is_shop() || is_product_category() || is_product_tag()) {
        // Check if our custom template exists
        $custom_archive = get_stylesheet_directory() . '/woocommerce/archive-product.php';
        if (file_exists($custom_archive)) {
            return $custom_archive;
        }
    }
    return $template;
}

// BEGIN ENQUEUE PARENT ACTION
// AUTO GENERATED - Do not modify or remove comment markers above or below:

if (!function_exists('chld_thm_cfg_locale_css')):
    function chld_thm_cfg_locale_css($uri) {
        if (empty($uri) && is_rtl() && file_exists(get_template_directory() . '/rtl.css'))
            $uri = get_template_directory_uri() . '/rtl.css';
        return $uri;
    }
endif;
add_filter('locale_stylesheet_uri', 'chld_thm_cfg_locale_css');

if (!function_exists('child_theme_configurator_css')):
    function child_theme_configurator_css() {
        wp_enqueue_style('chld_thm_cfg_child', trailingslashit(get_stylesheet_directory_uri()) . 'style.css', array(), '1.0.3');
    }
endif;
add_action('wp_enqueue_scripts', 'child_theme_configurator_css', 10);

// END ENQUEUE PARENT ACTION

/**
 * Load Vanatex Filter Manager Admin Tool
 */
if (is_admin()) {
    require_once get_stylesheet_directory() . '/admin/vanatex-filter-manager.php';
}