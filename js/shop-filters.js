// Complete WooCommerce Shop JavaScript - AJAX Filters + Collapse Functionality
document.addEventListener('DOMContentLoaded', function() {
    'use strict';

    // Check if we're on a shop page
    if (!document.querySelector('.custom-shop-container')) {
        return;
    }

    // Track whether price filter has been manually adjusted
    let priceFilterManuallyAdjusted = false;

    // Store original price values to detect changes
    const originalMinPrice = document.querySelector('input[name="min_price"]')?.value || '0';
    const originalMaxPrice = document.querySelector('input[name="max_price"]')?.value || '1010';

    // Initialize all functionality
    initializeCollapsibleFilters();
    initializeAjaxFiltering();
    initializeMobileToggle();
    initializeViewToggle();
    initializeAddToCart();
    initializePriceFilterTracking();

    // === PRICE FILTER TRACKING ===
    function initializePriceFilterTracking() {
        // Track price slider interactions
        const priceSlider = document.querySelector('.price_slider');
        if (priceSlider) {
            priceSlider.addEventListener('mousedown', function() {
                priceFilterManuallyAdjusted = true;
            });

            // For touch devices
            priceSlider.addEventListener('touchstart', function() {
                priceFilterManuallyAdjusted = true;
            });
        }

        // Track price input changes
        const minPriceInput = document.querySelector('input[name="min_price"]');
        const maxPriceInput = document.querySelector('input[name="max_price"]');

        if (minPriceInput) {
            minPriceInput.addEventListener('change', function() {
                if (this.value !== originalMinPrice) {
                    priceFilterManuallyAdjusted = true;
                }
            });
        }

        if (maxPriceInput) {
            maxPriceInput.addEventListener('change', function() {
                if (this.value !== originalMaxPrice) {
                    priceFilterManuallyAdjusted = true;
                }
            });
        }

        // Track price filter form submission
        const priceForm = document.querySelector('.price_slider_amount form');
        if (priceForm) {
            priceForm.addEventListener('submit', function() {
                const currentMin = document.querySelector('input[name="min_price"]')?.value;
                const currentMax = document.querySelector('input[name="max_price"]')?.value;

                if (currentMin !== originalMinPrice || currentMax !== originalMaxPrice) {
                    priceFilterManuallyAdjusted = true;
                }
            });
        }

        // Check URL for existing price filters
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.has('min_price') || urlParams.has('max_price')) {
            priceFilterManuallyAdjusted = true;
        }
    }

    // === COLLAPSIBLE FILTERS ===
    function initializeCollapsibleFilters() {
        const filterSections = document.querySelectorAll('.filter-section');

        filterSections.forEach((section) => {
            const title = section.querySelector('.filter-title, h4');
            if (!title) return;

            // Create filter header structure
            const header = document.createElement('div');
            header.className = 'filter-header';

            // Create toggle icon
            const toggle = document.createElement('span');
            toggle.className = 'filter-toggle';
            toggle.innerHTML = '+';

            // Clone title
            const titleClone = title.cloneNode(true);
            titleClone.className = 'filter-title';

            header.appendChild(titleClone);
            header.appendChild(toggle);

            // Create content wrapper
            const content = document.createElement('div');
            content.className = 'filter-content';

            // Move all content except title to content wrapper
            const children = Array.from(section.children);
            children.forEach(child => {
                if (child !== title) {
                    content.appendChild(child);
                }
            });

            // Replace original title with header
            title.replaceWith(header);
            section.appendChild(content);

            // Set initial state - only categories open by default
            const isCategoriesSection = section.classList.contains('categories-section') ||
                                      section.querySelector('.widget_product_categories') ||
                                      titleClone.textContent.toLowerCase().includes('categor');

            if (!isCategoriesSection) {
                section.classList.add('collapsed');
                toggle.innerHTML = '+';
            } else {
                toggle.innerHTML = '−';
            }

            // Add click event listener
            header.addEventListener('click', function() {
                toggleFilterSection(section, toggle);
            });
        });
    }

    function toggleFilterSection(section, toggle) {
        const isCollapsed = section.classList.contains('collapsed');

        if (isCollapsed) {
            // Expand
            section.classList.remove('collapsed');
            toggle.innerHTML = '−';

            const content = section.querySelector('.filter-content');
            if (content) {
                content.style.maxHeight = content.scrollHeight + 'px';
            }
        } else {
            // Collapse
            section.classList.add('collapsed');
            toggle.innerHTML = '+';

            const content = section.querySelector('.filter-content');
            if (content) {
                content.style.maxHeight = '0px';
            }
        }
    }

    // === AJAX FILTERING ===
    function initializeAjaxFiltering() {
        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Main filter handler
        function handleFilterChange() {
            showLoadingState();

            // Collect all filter data
            const filterData = collectFilterData();

            // Make AJAX request
            performAjaxRequest(filterData);
        }

        function collectFilterData() {
            const filterData = new URLSearchParams(window.location.search);

            // Stock status filters
            const stockFilters = document.querySelectorAll('input[name="stock_status"]:checked');
            if (stockFilters.length > 0) {
                const stockValues = Array.from(stockFilters).map(cb => cb.value);
                filterData.set('stock_status', stockValues.join(','));
            } else {
                filterData.delete('stock_status');
            }

            // Attribute filters
            const attributeCheckboxes = document.querySelectorAll('input[name^="filter_"]:checked');
            const attributeGroups = {};

            attributeCheckboxes.forEach(checkbox => {
                const name = checkbox.name.replace('[]', '');
                if (!attributeGroups[name]) {
                    attributeGroups[name] = [];
                }
                attributeGroups[name].push(checkbox.value);
            });

            // Clear existing attribute filters
            for (const [key] of filterData.entries()) {
                if (key.startsWith('filter_')) {
                    filterData.delete(key);
                }
            }

            // Add new attribute filters
            Object.entries(attributeGroups).forEach(([name, values]) => {
                filterData.set(name, values.join(','));
            });

            // Price filter - only add if manually adjusted
            if (priceFilterManuallyAdjusted) {
                const minPriceInput = document.querySelector('input[name="min_price"]');
                const maxPriceInput = document.querySelector('input[name="max_price"]');

                if (minPriceInput && minPriceInput.value) {
                    filterData.set('min_price', minPriceInput.value);
                } else {
                    filterData.delete('min_price');
                }

                if (maxPriceInput && maxPriceInput.value) {
                    filterData.set('max_price', maxPriceInput.value);
                } else {
                    filterData.delete('max_price');
                }
            } else {
                // Price not manually adjusted, remove from URL
                filterData.delete('min_price');
                filterData.delete('max_price');
            }

            // Search query
            const searchInput = document.querySelector('input[name="s"]');
            if (searchInput && searchInput.value) {
                filterData.set('s', searchInput.value);
            } else {
                filterData.delete('s');
            }

            return filterData;
        }

        function performAjaxRequest(filterData) {
            // Use Fetch API for AJAX request
            const url = window.location.pathname + '?' + filterData.toString();

            fetch(url, {
                method: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.text();
            })
            .then(html => {
                updatePageContent(html);
                updateURL(filterData);
                hideLoadingState();
            })
            .catch(error => {
                console.error('Filter error:', error);
                hideLoadingState();
                // Fallback to page reload
                window.location.href = url;
            });
        }

        function updatePageContent(html) {
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');

            // Store current view mode
            const productsGrid = document.getElementById('products-grid');
            const currentViewMode = productsGrid ? productsGrid.className : 'custom-product-grid';

            // Update products container
            const newProductsContainer = doc.querySelector('.products-container');
            const currentProductsContainer = document.querySelector('.products-container');

            if (newProductsContainer && currentProductsContainer) {
                currentProductsContainer.innerHTML = newProductsContainer.innerHTML;

                // Restore view mode
                const newProductsGrid = document.getElementById('products-grid');
                if (newProductsGrid) {
                    newProductsGrid.className = currentViewMode;
                }
            }

            // Update pagination
            const newPagination = doc.querySelector('.shop-pagination');
            const currentPagination = document.querySelector('.shop-pagination');

            if (newPagination && currentPagination) {
                currentPagination.innerHTML = newPagination.innerHTML;
            }

            // Update results count
            const newResultsCount = doc.querySelector('.results-count');
            const currentResultsCount = document.querySelector('.results-count');

            if (newResultsCount && currentResultsCount) {
                currentResultsCount.innerHTML = newResultsCount.innerHTML;
            }

            // Update active filters
            const newActiveFilters = doc.querySelector('.active-filters-list');
            const currentActiveFilters = document.querySelector('.active-filters-list');

            if (newActiveFilters && currentActiveFilters) {
                currentActiveFilters.innerHTML = newActiveFilters.innerHTML;
            }

            // Re-initialize add to cart buttons
            initializeAddToCart();
            // Re-initialize view toggle
            initializeViewToggle();
        }

        function updateURL(filterData) {
            const currentPath = window.location.pathname;
            let newUrl = currentPath;

            // Add params as query string
            const params = filterData.toString();
            if (params) {
                newUrl += (newUrl.indexOf('?') === -1 ? '?' : '&') + params;
            }

            // Update browser history
            history.pushState({
                path: newUrl
            }, '', newUrl);
        }

        function showLoadingState() {
            const productsContainer = document.querySelector('.products-container');
            if (productsContainer) {
                productsContainer.style.opacity = '0.5';
                productsContainer.style.pointerEvents = 'none';
            }

            // Add loading overlay
            let loadingOverlay = document.querySelector('.filter-loading-overlay');
            if (!loadingOverlay) {
                loadingOverlay = document.createElement('div');
                loadingOverlay.className = 'filter-loading-overlay';
                loadingOverlay.innerHTML = '<div class="loading-spinner">' +
                    (window.vanatexShopFilters && window.vanatexShopFilters.i18n ?
                     window.vanatexShopFilters.i18n.loadingText : 'Loading...') +
                    '</div>';

                const shopMain = document.querySelector('.custom-shop-main');
                if (shopMain) {
                    shopMain.style.position = 'relative';
                    shopMain.appendChild(loadingOverlay);
                }
            }
        }

        function hideLoadingState() {
            const productsContainer = document.querySelector('.products-container');
            if (productsContainer) {
                productsContainer.style.opacity = '1';
                productsContainer.style.pointerEvents = 'auto';
            }

            const loadingOverlay = document.querySelector('.filter-loading-overlay');
            if (loadingOverlay) {
                loadingOverlay.remove();
            }
        }

        // Initialize filter states from URL
        function initializeFiltersFromURL() {
            const urlParams = new URLSearchParams(window.location.search);

            // Stock status
            const stockStatus = urlParams.get('stock_status');
            if (stockStatus) {
                const stockValues = stockStatus.split(',');
                stockValues.forEach(value => {
                    const checkbox = document.querySelector(`input[name="stock_status"][value="${value}"]`);
                    if (checkbox) checkbox.checked = true;
                });
            }

            // Attribute filters
            for (const [key, value] of urlParams.entries()) {
                if (key.startsWith('filter_')) {
                    const attributeName = key;
                    const values = value.split(',');
                    values.forEach(val => {
                        const checkbox = document.querySelector(`input[name="${attributeName}[]"][value="${val}"]`);
                        if (checkbox) checkbox.checked = true;
                    });
                }
            }

            // Price filters
            const minPrice = urlParams.get('min_price');
            const maxPrice = urlParams.get('max_price');

            if (minPrice || maxPrice) {
                priceFilterManuallyAdjusted = true;
            }

            // Search
            const searchQuery = urlParams.get('s');
            if (searchQuery) {
                const searchInput = document.querySelector('input[name="s"]');
                if (searchInput) searchInput.value = searchQuery;
            }


        }

        // Attach event listeners
        const debouncedFilterChange = debounce(handleFilterChange, 300);

        // Filter checkboxes
        document.addEventListener('change', function(e) {
            if (e.target.matches('.filter-checkbox input[type="checkbox"]')) {
                debouncedFilterChange();
            }
        });

        // Price filter
        document.addEventListener('change', function(e) {
            if (e.target.matches('input[name="min_price"], input[name="max_price"]')) {
                priceFilterManuallyAdjusted = true;
                debounce(handleFilterChange, 500)();
            }
        });

        // Search form
        const searchForm = document.querySelector('.product-search-form');
        if (searchForm) {
            searchForm.addEventListener('submit', function(e) {
                e.preventDefault();
                handleFilterChange();
            });
        }

        // WooCommerce price filter form
        document.addEventListener('submit', function(e) {
            if (e.target.matches('.price_slider_amount form')) {
                e.preventDefault();
                priceFilterManuallyAdjusted = true;
                handleFilterChange();
            }
        });

        // Initialize filters from URL on page load
        initializeFiltersFromURL();
    }

    // === MOBILE TOGGLE ===
    function initializeMobileToggle() {
        const filterToggle = document.querySelector('.mobile-filter-toggle');
        const sidebar = document.querySelector('.custom-shop-sidebar');
        const closeSidebar = document.querySelector('.close-sidebar');

        if (filterToggle && sidebar) {
            filterToggle.addEventListener('click', function() {
                sidebar.classList.toggle('active');
                document.body.classList.toggle('sidebar-open');
            });
        }

        if (closeSidebar) {
            closeSidebar.addEventListener('click', function() {
                const sidebar = document.querySelector('.custom-shop-sidebar');
                if (sidebar) {
                    sidebar.classList.remove('active');
                    document.body.classList.remove('sidebar-open');
                }
            });
        }

        // Close sidebar when clicking outside
        document.addEventListener('click', function(e) {
            if (window.innerWidth <= 768) {
                const sidebar = document.querySelector('.custom-shop-sidebar');
                const mobileToggle = document.querySelector('.mobile-filter-toggle');

                if (sidebar &&
                    sidebar.classList.contains('active') &&
                    !sidebar.contains(e.target) &&
                    mobileToggle &&
                    !mobileToggle.contains(e.target)) {
                    sidebar.classList.remove('active');
                    document.body.classList.remove('sidebar-open');
                }
            }
        });
    }

    // === VIEW TOGGLE ===
    function initializeViewToggle() {
        const viewToggles = document.querySelectorAll('.view-toggle');
        const productsGrid = document.getElementById('products-grid');

        if (!productsGrid) return;

        viewToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                const view = this.dataset.view;

                // Update active state
                viewToggles.forEach(t => t.classList.remove('active'));
                this.classList.add('active');

                // Update grid class
                productsGrid.className = view === 'list' ? 'custom-product-list' : 'custom-product-grid';

                // Store view preference in local storage
                localStorage.setItem('shop_view_mode', view);
            });
        });

        // Initialize from saved preference
        const savedViewMode = localStorage.getItem('shop_view_mode');
        if (savedViewMode) {
            const toggle = document.querySelector(`.view-toggle[data-view="${savedViewMode}"]`);
            if (toggle) {
                toggle.click();
            }
        }
    }

    // === ADD TO CART ===
    function initializeAddToCart() {
        // Use event delegation for better performance
        document.removeEventListener('click', handleAddToCartClick);
        document.addEventListener('click', handleAddToCartClick);
    }

    function handleAddToCartClick(e) {
        if (!e.target.matches('.ajax-add-to-cart')) return;

        e.preventDefault();
        const button = e.target;
        const productId = button.dataset.productId;
        const nonce = button.dataset.nonce;

        if (!productId || button.classList.contains('loading')) return;

        button.classList.add('loading');
        button.textContent = window.vanatexShopFilters && window.vanatexShopFilters.i18n ?
                           window.vanatexShopFilters.i18n.addingText : 'Loading...';

        // Simple implementation - in production would use WooCommerce AJAX
        fetch(window.vanatexShopFilters.ajaxUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=woocommerce_ajax_add_to_cart&product_id=' + productId +
                  '&quantity=1&product_sku=&nonce=' + nonce
        })
        .then(response => response.json())
        .then(data => {
            button.classList.remove('loading');
            if (data.error) {
                button.textContent = window.vanatexShopFilters.i18n.addToCart;
                console.error('Add to cart error:', data.error);

                // Show error message
                const errorMessage = document.createElement('div');
                errorMessage.className = 'add-to-cart-error';
                errorMessage.textContent = data.error;
                button.parentNode.appendChild(errorMessage);

                // Auto-remove after 3 seconds
                setTimeout(() => {
                    errorMessage.remove();
                }, 3000);
            } else {
                button.textContent = window.vanatexShopFilters.i18n.addedText;
                button.classList.add('added');

                // Update mini cart if exists
                if (typeof updateMiniCart === 'function') {
                    updateMiniCart();
                }

                setTimeout(() => {
                    button.textContent = window.vanatexShopFilters.i18n.addToCart;
                    button.classList.remove('added');
                }, 2000);
            }
        })
        .catch(error => {
            console.error('Add to cart error:', error);
            button.classList.remove('loading');
            button.textContent = window.vanatexShopFilters.i18n.addToCart;
        });
    }

    // === GLOBAL FUNCTIONS ===
    window.clearAllFilters = function() {
        // Reset price filter tracking
        priceFilterManuallyAdjusted = false;

        // Redirect to base shop URL
        window.location.href = window.location.pathname;
    };

    window.clearFilter = function(filterType, filterValue = null) {
        const urlParams = new URLSearchParams(window.location.search);

        // Check if this is a price filter
        if (filterType === 'min_price' || filterType === 'max_price') {
            urlParams.delete(filterType);

            // If both price filters are cleared, reset the flag
            if (!urlParams.has('min_price') && !urlParams.has('max_price')) {
                priceFilterManuallyAdjusted = false;
            }
        }
        // Handle other filter types
        else if (filterValue) {
            const currentValues = urlParams.get(filterType);
            if (currentValues) {
                const values = currentValues.split(',').filter(v => v !== filterValue);
                if (values.length > 0) {
                    urlParams.set(filterType, values.join(','));
                } else {
                    urlParams.delete(filterType);
                }
            }
        } else {
            urlParams.delete(filterType);
        }

        const newUrl = window.location.pathname + (urlParams.toString() ? '?' + urlParams.toString() : '');
        window.location.href = newUrl;
    };
});